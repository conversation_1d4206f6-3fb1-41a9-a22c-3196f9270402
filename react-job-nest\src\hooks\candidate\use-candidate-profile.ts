import candidateApi from "@/services/candidate-api";
import { useAuthStore } from "@/stores/auth-store";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";

// Define types for the profile data
export interface Education {
  _id?: string;
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  startDate: string;
  endDate?: string;
  current?: boolean;
  description?: string;
}

export interface Experience {
  _id?: string;
  company: string;
  position: string;
  location?: string;
  startDate: string;
  endDate?: string;
  current?: boolean;
  description?: string;
}

export interface CandidateProfile {
  _id: string;
  user: string;
  headline?: string;
  summary?: string;
  skills: string[];
  experience: Experience[];
  education: Education[];
  phone?: string;
  location?: string;
  socialLinks?: {
    linkedin?: string;
    github?: string;
    twitter?: string;
    website?: string;
  };
  resumeUrl?: string;
  profileImageUrl?: string;
  profileCompleteness?: number;
  createdAt: string;
  updatedAt: string;
}

export interface UseCandidateProfileReturn {
  profile: CandidateProfile | null;
  isLoading: boolean;
  error: string | null;
  updateProfile: (data: Partial<CandidateProfile>) => Promise<boolean>;
  addSkill: (
    skill: string | { name: string; level: string },
  ) => Promise<boolean>;
  removeSkill: (skill: string) => Promise<boolean>;
  addEducation: (education: Partial<Education>) => Promise<boolean>;
  updateEducation: (
    id: string,
    education: Partial<Education>,
  ) => Promise<boolean>;
  deleteEducation: (id: string) => Promise<boolean>;
  addExperience: (experience: Partial<Experience>) => Promise<boolean>;
  updateExperience: (
    id: string,
    experience: Partial<Experience>,
  ) => Promise<boolean>;
  deleteExperience: (id: string) => Promise<boolean>;
  uploadResume: (file: File) => Promise<boolean>;
  refreshProfile: () => Promise<void>;
}

export default function useCandidateProfile(): UseCandidateProfileReturn {
  const [profile, setProfile] = useState<CandidateProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthStore();

  // Function to fetch the profile data
  const fetchProfile = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await candidateApi.profile.getMyProfile();

      if (response.data && response.data.success) {
        setProfile(response.data.profile);
      } else {
        setError("Failed to fetch profile data");
      }
    } catch (err: any) {
      console.error("Error fetching profile:", err);
      setError(err.message || "An error occurred while fetching your profile");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch profile on mount
  useEffect(() => {
    if (user) {
      fetchProfile();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // Update profile data
  const updateProfile = async (
    data: Partial<CandidateProfile>,
  ): Promise<boolean> => {
    try {
      setIsLoading(true);

      const response = await candidateApi.profile.updateProfile(data);

      if (response.data && response.data.success) {
        setProfile(response.data.profile);
        notifications.show({
          title: "Profile Updated",
          message: "Your profile has been successfully updated",
          color: "green",
        });
        return true;
      } else {
        throw new Error("Failed to update profile");
      }
    } catch (err: any) {
      console.error("Error updating profile:", err);
      setError(err.message || "An error occurred while updating your profile");
      notifications.show({
        title: "Update Failed",
        message: err.message || "Failed to update your profile",
        color: "red",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Add a skill
  const addSkill = async (
    skillData: string | { name: string; level: string },
  ): Promise<boolean> => {
    try {
      // For backward compatibility, extract just the name if an object is passed
      const skill = typeof skillData === "string" ? skillData : skillData.name;
      const level =
        typeof skillData === "string" ? "intermediate" : skillData.level;

      const response = await candidateApi.profile.addSkill({
        skill,
        level,
      });

      if (response.data && response.data.success) {
        // Update the local profile with the new skills
        if (profile) {
          setProfile({
            ...profile,
            skills: response.data.skills,
          });
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error adding skill:", err);
      return false;
    }
  };

  // Remove a skill
  const removeSkill = async (skill: string): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.removeSkill(skill);

      if (response.data && response.data.success) {
        // Update the local profile with the updated skills
        if (profile) {
          setProfile({
            ...profile,
            skills: profile.skills.filter((s) => s !== skill),
          });
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error removing skill:", err);
      return false;
    }
  };

  // Add education
  const addEducation = async (
    education: Partial<Education>,
  ): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.addEducation(education);

      if (response.data && response.data.success) {
        // Refresh the profile to get the updated education list
        await fetchProfile();
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error adding education:", err);
      return false;
    }
  };

  // Update education
  const updateEducation = async (
    id: string,
    education: Partial<Education>,
  ): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.updateEducation(
        id,
        education,
      );

      if (response.data && response.data.success) {
        // Refresh the profile to get the updated education list
        await fetchProfile();
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error updating education:", err);
      return false;
    }
  };

  // Delete education
  const deleteEducation = async (id: string): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.deleteEducation(id);

      if (response.data && response.data.success) {
        // Update the local profile with the updated education list
        if (profile) {
          setProfile({
            ...profile,
            education: profile.education.filter((edu) => edu._id !== id),
          });
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error deleting education:", err);
      return false;
    }
  };

  // Add experience
  const addExperience = async (
    experience: Partial<Experience>,
  ): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.addExperience(experience);

      if (response.data && response.data.success) {
        // Refresh the profile to get the updated experience list
        await fetchProfile();
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error adding experience:", err);
      return false;
    }
  };

  // Update experience
  const updateExperience = async (
    id: string,
    experience: Partial<Experience>,
  ): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.updateExperience(
        id,
        experience,
      );

      if (response.data && response.data.success) {
        // Refresh the profile to get the updated experience list
        await fetchProfile();
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error updating experience:", err);
      return false;
    }
  };

  // Delete experience
  const deleteExperience = async (id: string): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.deleteExperience(id);

      if (response.data && response.data.success) {
        // Update the local profile with the updated experience list
        if (profile) {
          setProfile({
            ...profile,
            experience: profile.experience.filter((exp) => exp._id !== id),
          });
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error deleting experience:", err);
      return false;
    }
  };

  // Upload resume
  const uploadResume = async (file: File): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.uploadResume(file);

      if (response.data && response.data.success) {
        // Update the local profile with the new resume URL
        if (profile) {
          setProfile({
            ...profile,
            resumeUrl: response.data.resumeUrl,
          });
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error uploading resume:", err);
      return false;
    }
  };

  // Upload profile image
  const uploadProfileImage = async (file: File): Promise<boolean> => {
    try {
      const response = await candidateApi.profile.uploadProfileImage(file);

      if (response.data && response.data.success) {
        // Update the local profile with the new profile image URL
        if (profile) {
          setProfile({
            ...profile,
            profileImageUrl: response.data.profileImageUrl,
          });
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error uploading profile image:", err);
      return false;
    }
  };

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    addSkill,
    removeSkill,
    addEducation,
    updateEducation,
    deleteEducation,
    addExperience,
    updateExperience,
    deleteExperience,
    uploadResume,
    uploadProfileImage,
    refreshProfile: fetchProfile,
  };
}
