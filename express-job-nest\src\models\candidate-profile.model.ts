import mongoose, { Document, Schema } from "mongoose";

export interface IEducation {
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  startDate: Date;
  endDate?: Date;
  description?: string;
  isCurrentlyStudying?: boolean;
}

export interface IExperience {
  company: string;
  position: string;
  location?: string;
  startDate: Date;
  endDate?: Date;
  description?: string;
  isCurrentlyWorking?: boolean;
}

export interface ICertification {
  name: string;
  issuingOrganization: string;
  issueDate: Date;
  expirationDate?: Date;
  credentialID?: string;
  credentialURL?: string;
}

export interface IReference {
  name: string;
  company: string;
  position: string;
  email: string;
  phone?: string;
  relationship: string;
}

export interface ISkillEndorsement {
  skill: string;
  endorser: mongoose.Types.ObjectId;
  date: Date;
}

export interface ISkill {
  name: string;
  level: string;
}

export interface ICandidateProfile extends Document {
  user: mongoose.Types.ObjectId;
  headline?: string;
  summary?: string;
  phone?: string;
  location?: string;
  skills: (string | ISkill)[];
  skillEndorsements?: ISkillEndorsement[];
  experience: IExperience[];
  education: IEducation[];
  certifications?: ICertification[];
  languages?: { language: string; proficiency: string }[];
  references?: IReference[];
  portfolioLinks?: { title: string; url: string }[];
  socialLinks?: {
    linkedin?: string;
    github?: string;
    twitter?: string;
    website?: string;
  };
  preferredJobTypes?: string[];
  preferredLocations?: string[];
  preferredSalary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  availability?: string;
  willingToRelocate?: boolean;
  resumeUrl?: string;
  profileCompleteness?: number;
  createdAt: Date;
  updatedAt: Date;
}

const educationSchema = new Schema<IEducation>({
  institution: {
    type: String,
    required: [true, "Institution name is required"],
    trim: true,
  },
  degree: {
    type: String,
    required: [true, "Degree is required"],
    trim: true,
  },
  fieldOfStudy: {
    type: String,
    trim: true,
  },
  startDate: {
    type: Date,
    required: [true, "Start date is required"],
  },
  endDate: {
    type: Date,
  },
  description: {
    type: String,
  },
  isCurrentlyStudying: {
    type: Boolean,
    default: false,
  },
});

const experienceSchema = new Schema<IExperience>({
  company: {
    type: String,
    required: [true, "Company name is required"],
    trim: true,
  },
  position: {
    type: String,
    required: [true, "Position is required"],
    trim: true,
  },
  location: {
    type: String,
    trim: true,
  },
  startDate: {
    type: Date,
    required: [true, "Start date is required"],
  },
  endDate: {
    type: Date,
  },
  description: {
    type: String,
  },
  isCurrentlyWorking: {
    type: Boolean,
    default: false,
  },
});

const certificationSchema = new Schema<ICertification>({
  name: {
    type: String,
    required: [true, "Certification name is required"],
    trim: true,
  },
  issuingOrganization: {
    type: String,
    required: [true, "Issuing organization is required"],
    trim: true,
  },
  issueDate: {
    type: Date,
    required: [true, "Issue date is required"],
  },
  expirationDate: {
    type: Date,
  },
  credentialID: {
    type: String,
  },
  credentialURL: {
    type: String,
  },
});

const referenceSchema = new Schema<IReference>({
  name: {
    type: String,
    required: [true, "Reference name is required"],
    trim: true,
  },
  company: {
    type: String,
    required: [true, "Company is required"],
    trim: true,
  },
  position: {
    type: String,
    required: [true, "Position is required"],
    trim: true,
  },
  email: {
    type: String,
    required: [true, "Email is required"],
    trim: true,
    lowercase: true,
  },
  phone: {
    type: String,
  },
  relationship: {
    type: String,
    required: [true, "Relationship is required"],
  },
});

const skillEndorsementSchema = new Schema<ISkillEndorsement>({
  skill: {
    type: String,
    required: [true, "Skill is required"],
  },
  endorser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: [true, "Endorser is required"],
  },
  date: {
    type: Date,
    default: Date.now,
  },
});

const candidateProfileSchema = new Schema<ICandidateProfile>(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "User is required"],
      unique: true,
    },
    headline: {
      type: String,
      trim: true,
    },
    summary: {
      type: String,
    },
    phone: {
      type: String,
      trim: true,
    },
    location: {
      type: String,
      trim: true,
    },
    skills: {
      type: [Schema.Types.Mixed],
      default: [],
    },
    skillEndorsements: {
      type: [skillEndorsementSchema],
      default: [],
    },
    experience: {
      type: [experienceSchema],
      default: [],
    },
    education: {
      type: [educationSchema],
      default: [],
    },
    certifications: {
      type: [certificationSchema],
      default: [],
    },
    languages: [
      {
        language: {
          type: String,
          required: true,
        },
        proficiency: {
          type: String,
          enum: ["Beginner", "Intermediate", "Advanced", "Native/Fluent"],
          required: true,
        },
      },
    ],
    references: {
      type: [referenceSchema],
      default: [],
    },
    portfolioLinks: [
      {
        title: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          required: true,
        },
      },
    ],
    socialLinks: {
      linkedin: String,
      github: String,
      twitter: String,
      website: String,
    },
    preferredJobTypes: {
      type: [String],
      default: [],
    },
    preferredLocations: {
      type: [String],
      default: [],
    },
    preferredSalary: {
      min: Number,
      max: Number,
      currency: {
        type: String,
        default: "USD",
      },
    },
    availability: {
      type: String,
      enum: ["Immediate", "2 weeks", "1 month", "3 months", "Not available"],
    },
    willingToRelocate: {
      type: Boolean,
      default: false,
    },
    resumeUrl: {
      type: String,
    },
    profileCompleteness: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better search performance
candidateProfileSchema.index({ skills: 1 });
candidateProfileSchema.index({ preferredJobTypes: 1 });
candidateProfileSchema.index({ preferredLocations: 1 });

const CandidateProfile = mongoose.model<ICandidateProfile>(
  "CandidateProfile",
  candidateProfileSchema
);

export default CandidateProfile;
