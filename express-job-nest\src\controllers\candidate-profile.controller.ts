import { Request, Response, NextFunction } from "express";
import CandidateProfile, { ISkill } from "../models/candidate-profile.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";
import User, { IUser } from "../models/user.model";
import {
  uploadTypes,
  deleteFile,
  getFileUrl,
} from "../services/file-upload.service";
import resumeParserService from "../services/resume-parser.service";
import Notification from "../models/notification.model";
import mongoose from "mongoose";

// Extend Express Request to include user property with proper typing
declare global {
  namespace Express {
    interface Request {
      user: IUser;
    }
  }
}

// Get current user's candidate profile
export const getMyProfile = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    // If no profile exists, create a new one
    if (!profile) {
      const newProfile = await CandidateProfile.create({
        user: req.user._id,
      });

      return res.status(200).json({
        success: true,
        profile: newProfile,
      });
    }

    res.status(200).json({
      success: true,
      profile,
    });
  }
);

// Update current user's candidate profile
export const updateMyProfile = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Find the profile for the current user
    let profile = await CandidateProfile.findOne({ user: req.user._id });

    // If no profile exists, create a new one
    if (!profile) {
      profile = await CandidateProfile.create({
        user: req.user._id,
        ...req.body,
      });

      return res.status(201).json({
        success: true,
        profile,
      });
    }

    // Update the profile
    const updatedProfile = await CandidateProfile.findOneAndUpdate(
      { user: req.user._id },
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedProfile) {
      return res.status(404).json({
        success: false,
        message: "Profile not found",
      });
    }

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(updatedProfile);
    updatedProfile.profileCompleteness = completeness;
    await updatedProfile.save();

    res.status(200).json({
      success: true,
      profile: updatedProfile,
    });
  }
);

// Add education to candidate profile
export const addEducation = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Add education to the profile
    profile.education.push(req.body);
    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      education: profile.education,
    });
  }
);

// Update education in candidate profile
export const updateEducation = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { educationId } = req.params;

    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Find the education item
    const educationIndex = profile.education.findIndex(
      (edu) => (edu as any)._id.toString() === educationId
    );

    if (educationIndex === -1) {
      return next(new AppError("Education not found", 404));
    }

    // Update the education item
    profile.education[educationIndex] = {
      ...(profile.education[educationIndex] as any).toObject(),
      ...req.body,
    };

    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      education: profile.education,
    });
  }
);

// Delete education from candidate profile
export const deleteEducation = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { educationId } = req.params;

    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Remove the education item
    profile.education = profile.education.filter(
      (edu) => (edu as any)._id.toString() !== educationId
    );

    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      education: profile.education,
    });
  }
);

// Add experience to candidate profile
export const addExperience = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Add experience to the profile
    profile.experience.push(req.body);
    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      experience: profile.experience,
    });
  }
);

// Update experience in candidate profile
export const updateExperience = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { experienceId } = req.params;

    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Find the experience item
    const experienceIndex = profile.experience.findIndex(
      (exp) => (exp as any)._id.toString() === experienceId
    );

    if (experienceIndex === -1) {
      return next(new AppError("Experience not found", 404));
    }

    // Update the experience item
    profile.experience[experienceIndex] = {
      ...(profile.experience[experienceIndex] as any).toObject(),
      ...req.body,
    };

    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      experience: profile.experience,
    });
  }
);

// Delete experience from candidate profile
export const deleteExperience = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { experienceId } = req.params;

    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Remove the experience item
    profile.experience = profile.experience.filter(
      (exp) => (exp as any)._id.toString() !== experienceId
    );

    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      experience: profile.experience,
    });
  }
);

// Get candidate profile by user ID (for employers and admins)
export const getCandidateProfileByUserId = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { userId } = req.params;

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check if the user is a candidate
    if (user.role !== "candidate") {
      return next(new AppError("User is not a candidate", 400));
    }

    // Find the profile
    const profile = await CandidateProfile.findOne({ user: userId });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    res.status(200).json({
      success: true,
      profile,
    });
  }
);

// Add skill to candidate profile
export const addSkill = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { skill, level } = req.body;

    if (!skill) {
      return next(new AppError("Skill is required", 400));
    }

    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Check if skill already exists (for string skills)
    if (typeof profile.skills[0] === "string") {
      if (profile.skills.includes(skill)) {
        return next(new AppError("Skill already exists in your profile", 400));
      }

      // If we're adding a skill with level but current skills are strings,
      // convert existing skills to objects with default level
      if (level) {
        profile.skills = profile.skills.map((s) => {
          return {
            name: s as string,
            level: "intermediate",
          };
        });
      }
    } else {
      // For object skills, check if name already exists
      const skillExists = profile.skills.some(
        (s) => typeof s === "object" && (s as ISkill).name === skill
      );

      if (skillExists) {
        return next(new AppError("Skill already exists in your profile", 400));
      }
    }

    // Add skill to the profile (either as string or object with level)
    if (level) {
      const skillObj: ISkill = {
        name: skill,
        level: level,
      };
      profile.skills.push(skillObj);
    } else {
      profile.skills.push(skill);
    }

    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      skills: profile.skills,
    });
  }
);

// Remove skill from candidate profile
export const removeSkill = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { skill } = req.params;

    // Find the profile for the current user
    const profile = await CandidateProfile.findOne({ user: req.user._id });

    if (!profile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Check if skills are strings or objects
    if (typeof profile.skills[0] === "string") {
      // Check if skill exists
      if (!profile.skills.includes(skill)) {
        return next(new AppError("Skill not found in your profile", 404));
      }

      // Remove skill from the profile
      profile.skills = profile.skills.filter((s) => s !== skill);
    } else {
      // For object skills, check if name exists
      const skillExists = profile.skills.some(
        (s) => typeof s === "object" && (s as ISkill).name === skill
      );

      if (!skillExists) {
        return next(new AppError("Skill not found in your profile", 404));
      }

      // Remove skill from the profile
      profile.skills = profile.skills.filter(
        (s) => !(typeof s === "object" && (s as ISkill).name === skill)
      );
    }

    await profile.save();

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(profile);
    profile.profileCompleteness = completeness;
    await profile.save();

    res.status(200).json({
      success: true,
      skills: profile.skills,
    });
  }
);

// Endorse a skill for a candidate (by employers)
export const endorseSkill = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { candidateId, skill } = req.body;

    if (!candidateId || !skill) {
      return next(new AppError("Candidate ID and skill are required", 400));
    }

    // Check if the candidate exists
    const candidateUser = await User.findById(candidateId);
    if (!candidateUser || candidateUser.role !== "candidate") {
      return next(new AppError("Candidate not found", 404));
    }

    // Find the candidate's profile
    const candidateProfile = await CandidateProfile.findOne({
      user: candidateId,
    });
    if (!candidateProfile) {
      return next(new AppError("Candidate profile not found", 404));
    }

    // Check if the skill exists in the candidate's profile
    const skillExists =
      typeof candidateProfile.skills[0] === "string"
        ? candidateProfile.skills.includes(skill)
        : candidateProfile.skills.some(
            (s) => typeof s === "object" && (s as ISkill).name === skill
          );

    if (!skillExists) {
      return next(new AppError("Skill not found in candidate's profile", 404));
    }

    // Initialize skillEndorsements array if it doesn't exist
    if (!candidateProfile.skillEndorsements) {
      candidateProfile.skillEndorsements = [];
    }

    // Check if the endorsement already exists
    const endorsementExists = candidateProfile.skillEndorsements?.some(
      (endorsement) =>
        endorsement.skill === skill &&
        endorsement.endorser.toString() === req.user._id?.toString()
    );

    if (endorsementExists) {
      return next(new AppError("You have already endorsed this skill", 400));
    }

    // Initialize skillEndorsements array if it doesn't exist
    if (!candidateProfile.skillEndorsements) {
      candidateProfile.skillEndorsements = [];
    }

    // Add the endorsement
    candidateProfile.skillEndorsements.push({
      skill,
      endorser: req.user._id as unknown as mongoose.Types.ObjectId,
      date: new Date(),
    });

    await candidateProfile.save();

    res.status(200).json({
      success: true,
      message: `You have successfully endorsed ${candidateUser.name} for ${skill}`,
    });
  }
);

// Helper function to calculate profile completeness
const calculateProfileCompleteness = (profile: any): number => {
  let score = 0;
  let totalFields = 0;

  // Basic information
  if (profile.headline) {
    score++;
  }
  if (profile.summary) {
    score++;
  }
  if (profile.phone) {
    score++;
  }
  if (profile.location) {
    score++;
  }
  totalFields += 4;

  // Skills
  if (profile.skills && profile.skills.length > 0) {
    score++;
    // Bonus points for having skills with levels
    if (
      profile.skills.some(
        (skill: string | ISkill) =>
          typeof skill === "object" && (skill as ISkill).level
      )
    ) {
      score++;
    }
    // Bonus points for having endorsed skills
    if (profile.skillEndorsements && profile.skillEndorsements.length > 0) {
      score++;
    }
  }
  totalFields += 3;

  // Experience
  if (profile.experience && profile.experience.length > 0) {
    score += 2;
  }
  totalFields += 2;

  // Education
  if (profile.education && profile.education.length > 0) {
    score += 2;
  }
  totalFields += 2;

  // Certifications
  if (profile.certifications && profile.certifications.length > 0) {
    score++;
  }
  totalFields++;

  // Languages
  if (profile.languages && profile.languages.length > 0) {
    score++;
  }
  totalFields++;

  // References
  if (profile.references && profile.references.length > 0) {
    score++;
  }
  totalFields++;

  // Portfolio links
  if (profile.portfolioLinks && profile.portfolioLinks.length > 0) {
    score++;
  }
  totalFields++;

  // Social links
  if (profile.socialLinks) {
    if (profile.socialLinks.linkedin) {
      score++;
    }
    if (profile.socialLinks.github) {
      score++;
    }
    if (profile.socialLinks.twitter) {
      score++;
    }
    if (profile.socialLinks.website) {
      score++;
    }
    totalFields += 4;
  }

  // Job preferences
  if (profile.preferredJobTypes && profile.preferredJobTypes.length > 0) {
    score++;
  }
  if (profile.preferredLocations && profile.preferredLocations.length > 0) {
    score++;
  }
  if (
    profile.preferredSalary &&
    (profile.preferredSalary.min || profile.preferredSalary.max)
  ) {
    score++;
  }
  if (profile.availability) {
    score++;
  }
  totalFields += 4;

  // Resume
  if (profile.resumeUrl) {
    score++;
  }
  totalFields++;

  // Calculate percentage
  return Math.round((score / totalFields) * 100);
};

// Upload resume middleware
export const uploadResume = uploadTypes.resume.single("resume");

// Upload and parse resume
export const uploadAndParseResume = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Check if file was uploaded
    if (!req.file) {
      return next(new AppError("No resume file uploaded", 400));
    }

    // Get the file path
    const filePath = `uploads/resumes/${req.file.filename}`;
    const fileUrl = getFileUrl(filePath);

    try {
      // Find the candidate profile
      const profile = await CandidateProfile.findOne({
        user: (req.user as any)._id,
      });

      if (!profile) {
        return next(new AppError("Candidate profile not found", 404));
      }

      // Delete old resume if exists
      if (profile.resumeUrl) {
        const oldFilePath = profile.resumeUrl.replace(
          /^\/uploads\//,
          "uploads/"
        );
        await deleteFile(oldFilePath);
      }

      // Update profile with new resume URL
      profile.resumeUrl = fileUrl;

      // Parse the resume
      const parsedData = await resumeParserService.parseResume(filePath);

      // Update profile with parsed data if fields are empty
      if (
        parsedData.skills &&
        parsedData.skills.length > 0 &&
        (!profile.skills || profile.skills.length === 0)
      ) {
        profile.skills = parsedData.skills;
      }

      if (
        parsedData.experience &&
        parsedData.experience.length > 0 &&
        (!profile.experience || profile.experience.length === 0)
      ) {
        // Add missing required fields
        const validExperiences = parsedData.experience
          .filter((exp) => exp.position && exp.company)
          .map((exp) => ({
            company: exp.company || "",
            position: exp.position || "",
            description: exp.description || "",
            location: exp.location || "",
            startDate: exp.startDate || new Date(),
            endDate: exp.endDate || new Date(),
            isCurrentlyWorking: false,
          }));

        if (validExperiences.length > 0) {
          profile.experience = validExperiences;
        }
      }

      if (
        parsedData.education &&
        parsedData.education.length > 0 &&
        (!profile.education || profile.education.length === 0)
      ) {
        // Add missing required fields
        const validEducation = parsedData.education
          .filter((edu) => edu.institution && edu.degree)
          .map((edu) => ({
            institution: edu.institution || "",
            degree: edu.degree || "",
            fieldOfStudy: edu.fieldOfStudy || "",
            startDate: edu.startDate || new Date(),
            endDate: edu.endDate || new Date(),
            description: "",
            isCurrentlyStudying: false,
          }));

        if (validEducation.length > 0) {
          profile.education = validEducation;
        }
      }

      // Update profile completeness
      profile.profileCompleteness = calculateProfileCompleteness(profile);

      // Save the updated profile
      await profile.save();

      // Send notification
      try {
        // Create notification data
        const notificationData = {
          recipient: (req.user as any)._id,
          type: "account_update" as const,
          title: "Resume Uploaded",
          message: "Your resume has been uploaded and parsed successfully.",
          metadata: {
            profileCompleteness: profile.profileCompleteness,
          },
          isEmailSent: false,
        };

        // Create notification directly without using the controller function
        await Notification.create(notificationData);
      } catch (error) {
        console.error("Failed to send notification:", error);
      }

      res.status(200).json({
        success: true,
        message: "Resume uploaded and parsed successfully",
        resumeUrl: fileUrl,
        parsedData: {
          skills: parsedData.skills,
          experienceCount: parsedData.experience.length,
          educationCount: parsedData.education.length,
        },
        profileCompleteness: profile.profileCompleteness,
      });
    } catch (error) {
      // Delete the uploaded file if there was an error
      await deleteFile(filePath);
      return next(
        new AppError(
          `Error processing resume: ${(error as Error).message}`,
          500
        )
      );
    }
  }
);
